"""
Stock-related API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..database import get_db
from ..models.user import User
from ..schemas.stock import (
    StockResponse, StockCreate, StockUpdate, StockHistory,
    StockScreeningCriteria, StockScreeningResult, StockSearchResult,
    EntryPointAnalysis, TechnicalIndicatorsResponse, TradingSignalsResponse,
    SpecializedScreeningResponse
)
from ..services.stock_service import stock_service
from ..services.market_data_service import market_data_service
from ..core.security import get_current_active_user
from ..core.exceptions import handle_exceptions, StockNotFoundError
from ..utils.logger import log_api_call

router = APIRouter()


@router.get("/", response_model=List[StockResponse])
@handle_exceptions
async def get_stocks(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    exchange: Optional[str] = Query(None, description="Filter by exchange"),
    search: Optional[str] = Query(None, description="Search by symbol or name"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of stocks with optional filtering."""
    log_api_call("/stocks", "GET", user_id=current_user.id)

    stocks = await stock_service.get_stocks(
        db=db,
        skip=skip,
        limit=limit,
        sector=sector,
        exchange=exchange,
        search=search
    )

    return [StockResponse.from_orm(stock) for stock in stocks]


@router.get("/search", response_model=List[StockSearchResult])
@handle_exceptions
async def search_stocks(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    current_user: User = Depends(get_current_active_user)
):
    """Search for stocks by symbol or name."""
    log_api_call("/stocks/search", "GET", user_id=current_user.id)

    # Use enhanced market data service for search
    results = await market_data_service.search_stocks(q, limit)
    return [StockSearchResult(**result) for result in results]


@router.get("/trending", response_model=List[Dict[str, Any]])
@handle_exceptions
async def get_trending_stocks(
    limit: int = Query(20, ge=1, le=50, description="Maximum number of trending stocks"),
    current_user: User = Depends(get_current_active_user)
):
    """Get trending/most active stocks."""
    log_api_call("/stocks/trending", "GET", user_id=current_user.id)

    trending_stocks = await market_data_service.get_trending_stocks(limit)
    return trending_stocks


@router.get("/market-status", response_model=Dict[str, Any])
@handle_exceptions
async def get_market_status(
    current_user: User = Depends(get_current_active_user)
):
    """Get current market status and trading hours."""
    log_api_call("/stocks/market-status", "GET", user_id=current_user.id)

    market_status = await market_data_service.get_market_status()
    return market_status


@router.post("/bulk-quotes", response_model=Dict[str, Any])
@handle_exceptions
async def get_bulk_quotes(
    symbols: List[str] = Body(..., description="List of stock symbols"),
    current_user: User = Depends(get_current_active_user)
):
    """Get quotes for multiple stocks efficiently."""
    log_api_call("/stocks/bulk-quotes", "POST", user_id=current_user.id)

    if len(symbols) > 100:
        raise HTTPException(status_code=400, detail="Maximum 100 symbols allowed per request")

    # Sanitize symbols
    clean_symbols = [symbol.upper().strip() for symbol in symbols if symbol.strip()]

    quotes = await market_data_service.get_multiple_quotes(clean_symbols)

    return {
        "requested_symbols": len(clean_symbols),
        "successful_quotes": len(quotes),
        "quotes": quotes,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/{symbol}", response_model=StockResponse)
@handle_exceptions
async def get_stock_detail(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get detailed information for a specific stock."""
    log_api_call(f"/stocks/{symbol}", "GET", user_id=current_user.id)

    stock = await stock_service.get_stock_by_symbol(db, symbol)
    if not stock:
        raise StockNotFoundError(f"Stock with symbol {symbol} not found")

    return StockResponse.from_orm(stock)


@router.get("/{symbol}/history", response_model=StockHistory)
@handle_exceptions
async def get_stock_history(
    symbol: str,
    timeframe: str = Query("1d", pattern="^(1m|5m|15m|1h|1d|1w|1M)$", description="Data timeframe"),
    limit: int = Query(100, ge=1, le=1000, description="Number of data points"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get historical price data for a stock."""
    log_api_call(f"/stocks/{symbol}/history", "GET", user_id=current_user.id)

    # Verify stock exists
    stock = await stock_service.get_stock_by_symbol(db, symbol)
    if not stock:
        raise StockNotFoundError(f"Stock with symbol {symbol} not found")

    history = await stock_service.get_stock_history(db, symbol, timeframe, limit)
    if not history:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Historical data not available"
        )

    return history


@router.post("/screen", response_model=StockScreeningResult)
@handle_exceptions
async def screen_stocks(
    criteria: StockScreeningCriteria,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Screen stocks based on criteria."""
    log_api_call("/stocks/screen", "POST", user_id=current_user.id)

    result = await stock_service.screen_stocks(db, criteria)
    return result


@router.get("/{symbol}/entry-analysis", response_model=EntryPointAnalysis)
@handle_exceptions
async def analyze_entry_point(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Analyze entry point for a specific stock."""
    log_api_call(f"/stocks/{symbol}/entry-analysis", "GET", user_id=current_user.id)

    analysis = await stock_service.analyze_entry_point(db, symbol.upper())
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Entry point analysis not available - insufficient data or analysis error"
        )

    return analysis


@router.get("/{symbol}/technical-indicators")
@handle_exceptions
async def get_technical_indicators(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get technical indicators for a specific stock."""
    log_api_call(f"/stocks/{symbol}/technical-indicators", "GET", user_id=current_user.id)

    # Verify stock exists
    stock = await stock_service.get_stock_by_symbol(db, symbol.upper())
    if not stock:
        raise StockNotFoundError(f"Stock with symbol {symbol} not found")

    # Check if technical indicators are available
    if not stock.technical_indicators or 'indicators' not in stock.technical_indicators:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Technical indicators not available - data may be updating"
        )

    indicators = stock.technical_indicators['indicators']

    return {
        "symbol": symbol.upper(),
        "timestamp": stock.technical_indicators.get('last_updated'),
        "indicators": indicators
    }


@router.get("/{symbol}/trading-signals")
@handle_exceptions
async def get_trading_signals(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get trading signals for a specific stock."""
    log_api_call(f"/stocks/{symbol}/trading-signals", "GET", user_id=current_user.id)

    # Verify stock exists
    stock = await stock_service.get_stock_by_symbol(db, symbol.upper())
    if not stock:
        raise StockNotFoundError(f"Stock with symbol {symbol} not found")

    # Check if technical indicators are available
    if not stock.technical_indicators or 'signals' not in stock.technical_indicators:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Trading signals not available - data may be updating"
        )

    signals = stock.technical_indicators['signals']

    return {
        "symbol": symbol.upper(),
        "timestamp": stock.technical_indicators.get('last_updated'),
        "signals": signals
    }


@router.get("/momentum")
@handle_exceptions
async def get_momentum_stocks(
    min_price_change: float = Query(5.0, ge=0, description="Minimum price change percentage"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get momentum stocks with significant price movement."""
    log_api_call("/stocks/momentum", "GET", user_id=current_user.id)

    try:
        from ..algorithms.screening_algorithms import screen_momentum_stocks
        momentum_stocks = screen_momentum_stocks(db, min_price_change)[:limit]

        return {
            "criteria": {
                "min_price_change": min_price_change,
                "limit": limit
            },
            "results": [StockResponse.from_orm(stock) for stock in momentum_stocks],
            "total_matches": len(momentum_stocks),
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        logger.error(f"Error getting momentum stocks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving momentum stocks"
        )


@router.get("/oversold")
@handle_exceptions
async def get_oversold_stocks(
    rsi_threshold: float = Query(30.0, ge=0, le=100, description="RSI threshold for oversold condition"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get potentially oversold stocks based on RSI."""
    log_api_call("/stocks/oversold", "GET", user_id=current_user.id)

    try:
        from ..algorithms.screening_algorithms import screen_oversold_stocks
        oversold_stocks = screen_oversold_stocks(db, rsi_threshold)[:limit]

        return {
            "criteria": {
                "rsi_threshold": rsi_threshold,
                "limit": limit
            },
            "results": [StockResponse.from_orm(stock) for stock in oversold_stocks],
            "total_matches": len(oversold_stocks),
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        logger.error(f"Error getting oversold stocks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving oversold stocks"
        )


@router.get("/breakout")
@handle_exceptions
async def get_breakout_stocks(
    volume_threshold: float = Query(2.0, ge=1.0, description="Volume surge threshold"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get potential breakout stocks based on volume surge."""
    log_api_call("/stocks/breakout", "GET", user_id=current_user.id)

    try:
        from ..algorithms.screening_algorithms import screen_breakout_stocks
        breakout_stocks = screen_breakout_stocks(db, volume_threshold)[:limit]

        return {
            "criteria": {
                "volume_threshold": volume_threshold,
                "limit": limit
            },
            "results": [StockResponse.from_orm(stock) for stock in breakout_stocks],
            "total_matches": len(breakout_stocks),
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        logger.error(f"Error getting breakout stocks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving breakout stocks"
        )


@router.post("/", response_model=StockResponse, status_code=status.HTTP_201_CREATED)
@handle_exceptions
async def create_stock(
    stock_data: StockCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new stock (admin only)."""
    log_api_call("/stocks", "POST", user_id=current_user.id)

    # TODO: Add admin role check
    stock = await stock_service.create_stock(db, stock_data)
    return StockResponse.from_orm(stock)


@router.put("/{stock_id}", response_model=StockResponse)
@handle_exceptions
async def update_stock(
    stock_id: int,
    stock_data: StockUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update stock information (admin only)."""
    log_api_call(f"/stocks/{stock_id}", "PUT", user_id=current_user.id)

    # TODO: Add admin role check
    stock = await stock_service.update_stock(db, stock_id, stock_data)
    if not stock:
        raise StockNotFoundError(f"Stock with ID {stock_id} not found")

    return StockResponse.from_orm(stock)
