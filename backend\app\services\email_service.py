"""
Email notification service for sending alert notifications.
"""
import async<PERSON>
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from jinja2 import Environment, BaseLoader
from typing import Dict, Any, Optional
from datetime import datetime
import logging

from ..config import settings
from ..models.alert import <PERSON><PERSON>, AlertNotification
from ..models.user import User
from ..database import SessionLocal
from ..utils.logger import get_logger

logger = get_logger(__name__)


class EmailService:
    """Service for sending email notifications."""
    
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_email = settings.FROM_EMAIL
        self.jinja_env = Environment(loader=BaseLoader())
        
    async def send_alert_notification(self, alert: Alert, user: User) -> bool:
        """Send alert notification email to user."""
        try:
            if not user.notification_email or not user.email:
                logger.info(f"Email notifications disabled for user {user.id}")
                return False
                
            if not self._is_configured():
                logger.warning("Email service not configured, skipping email notification")
                return False
            
            # Create email content
            subject = f"EntryAlert: {alert.title}"
            html_content = self._create_alert_email_html(alert, user)
            text_content = self._create_alert_email_text(alert, user)
            
            # Send email
            success = await self._send_email(
                to_email=user.email,
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )
            
            # Log notification attempt
            await self._log_notification(alert.id, user.email, "email", success)
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending alert email to {user.email}: {e}")
            await self._log_notification(alert.id, user.email, "email", False, str(e))
            return False
    
    async def send_welcome_email(self, user: User) -> bool:
        """Send welcome email to new user."""
        try:
            if not self._is_configured():
                logger.warning("Email service not configured, skipping welcome email")
                return False
            
            subject = f"Welcome to {settings.APP_NAME}!"
            html_content = self._create_welcome_email_html(user)
            text_content = self._create_welcome_email_text(user)
            
            return await self._send_email(
                to_email=user.email,
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )
            
        except Exception as e:
            logger.error(f"Error sending welcome email to {user.email}: {e}")
            return False
    
    async def send_password_reset_email(self, user: User, reset_token: str) -> bool:
        """Send password reset email to user."""
        try:
            if not self._is_configured():
                logger.warning("Email service not configured, skipping password reset email")
                return False
            
            subject = f"{settings.APP_NAME} - Password Reset Request"
            html_content = self._create_password_reset_email_html(user, reset_token)
            text_content = self._create_password_reset_email_text(user, reset_token)
            
            return await self._send_email(
                to_email=user.email,
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )
            
        except Exception as e:
            logger.error(f"Error sending password reset email to {user.email}: {e}")
            return False
    
    def _is_configured(self) -> bool:
        """Check if email service is properly configured."""
        return all([
            self.smtp_host,
            self.smtp_port,
            self.smtp_user,
            self.smtp_password,
            self.from_email
        ])
    
    async def _send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: str
    ) -> bool:
        """Send email using SMTP."""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = self.from_email
            message["To"] = to_email
            
            # Add text and HTML parts
            text_part = MIMEText(text_content, "plain")
            html_part = MIMEText(html_content, "html")
            
            message.attach(text_part)
            message.attach(html_part)
            
            # Send email
            await aiosmtplib.send(
                message,
                hostname=self.smtp_host,
                port=self.smtp_port,
                start_tls=True,
                username=self.smtp_user,
                password=self.smtp_password,
            )
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def _create_alert_email_html(self, alert: Alert, user: User) -> str:
        """Create HTML content for alert notification email."""
        template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{{ alert_title }}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #3B82F6; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .alert-box { background-color: white; border-left: 4px solid #3B82F6; padding: 15px; margin: 15px 0; }
                .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background-color: #3B82F6; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{{ app_name }}</h1>
                    <p>Stock Alert Notification</p>
                </div>
                <div class="content">
                    <h2>Hello {{ user_name }},</h2>
                    <div class="alert-box">
                        <h3>{{ alert_title }}</h3>
                        <p><strong>Stock:</strong> {{ stock_symbol }}</p>
                        <p><strong>Alert Type:</strong> {{ alert_type }}</p>
                        <p><strong>Message:</strong> {{ alert_message }}</p>
                        {% if trigger_price %}
                        <p><strong>Trigger Price:</strong> ${{ trigger_price }}</p>
                        {% endif %}
                        <p><strong>Triggered At:</strong> {{ triggered_at }}</p>
                    </div>
                    <p>This alert was triggered based on your configured conditions. You can manage your alerts by logging into your account.</p>
                    <p style="text-align: center;">
                        <a href="{{ app_url }}" class="button">View Dashboard</a>
                    </p>
                </div>
                <div class="footer">
                    <p>This email was sent by {{ app_name }}. If you no longer wish to receive these notifications, you can disable them in your account settings.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        template_obj = self.jinja_env.from_string(template)
        return template_obj.render(
            app_name=settings.APP_NAME,
            app_url=f"http://localhost:3000",  # TODO: Make this configurable
            user_name=user.first_name or user.username,
            alert_title=alert.title,
            alert_message=alert.message,
            alert_type=alert.alert_type.value.replace('_', ' ').title(),
            stock_symbol=alert.stock.symbol if alert.stock else "Unknown",
            trigger_price=alert.trigger_price,
            triggered_at=alert.triggered_at.strftime("%Y-%m-%d %H:%M:%S UTC") if alert.triggered_at else "Unknown"
        )
    
    def _create_alert_email_text(self, alert: Alert, user: User) -> str:
        """Create text content for alert notification email."""
        return f"""
{settings.APP_NAME} - Stock Alert Notification

Hello {user.first_name or user.username},

Your alert has been triggered:

Alert: {alert.title}
Stock: {alert.stock.symbol if alert.stock else "Unknown"}
Type: {alert.alert_type.value.replace('_', ' ').title()}
Message: {alert.message}
{f"Trigger Price: ${alert.trigger_price}" if alert.trigger_price else ""}
Triggered At: {alert.triggered_at.strftime("%Y-%m-%d %H:%M:%S UTC") if alert.triggered_at else "Unknown"}

You can manage your alerts by logging into your account at: http://localhost:3000

---
This email was sent by {settings.APP_NAME}. If you no longer wish to receive these notifications, you can disable them in your account settings.
        """.strip()
    
    def _create_welcome_email_html(self, user: User) -> str:
        """Create HTML content for welcome email."""
        template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Welcome to {{ app_name }}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #3B82F6; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background-color: #3B82F6; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to {{ app_name }}!</h1>
                </div>
                <div class="content">
                    <h2>Hello {{ user_name }},</h2>
                    <p>Welcome to {{ app_name }}, your intelligent stock screening and alert platform!</p>
                    <p>You can now:</p>
                    <ul>
                        <li>Create custom watchlists</li>
                        <li>Set up intelligent alerts</li>
                        <li>Screen stocks with advanced algorithms</li>
                        <li>Get real-time notifications</li>
                    </ul>
                    <p style="text-align: center;">
                        <a href="{{ app_url }}" class="button">Get Started</a>
                    </p>
                </div>
                <div class="footer">
                    <p>Thank you for joining {{ app_name }}!</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        template_obj = self.jinja_env.from_string(template)
        return template_obj.render(
            app_name=settings.APP_NAME,
            app_url=f"http://localhost:3000",  # TODO: Make this configurable
            user_name=user.first_name or user.username
        )
    
    def _create_welcome_email_text(self, user: User) -> str:
        """Create text content for welcome email."""
        return f"""
Welcome to {settings.APP_NAME}!

Hello {user.first_name or user.username},

Welcome to {settings.APP_NAME}, your intelligent stock screening and alert platform!

You can now:
- Create custom watchlists
- Set up intelligent alerts  
- Screen stocks with advanced algorithms
- Get real-time notifications

Get started at: http://localhost:3000

Thank you for joining {settings.APP_NAME}!
        """.strip()

    def _create_password_reset_email_html(self, user: User, reset_token: str) -> str:
        """Create HTML content for password reset email."""
        template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Password Reset - {{ app_name }}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #3B82F6; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background-color: #3B82F6; color: white; text-decoration: none; border-radius: 5px; }
                .warning { background-color: #FEF3C7; border: 1px solid #F59E0B; padding: 10px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{{ app_name }}</h1>
                    <p>Password Reset Request</p>
                </div>
                <div class="content">
                    <h2>Hello {{ user_name }},</h2>
                    <p>We received a request to reset your password for your {{ app_name }} account.</p>
                    <p style="text-align: center;">
                        <a href="{{ reset_url }}" class="button">Reset Password</a>
                    </p>
                    <div class="warning">
                        <p><strong>Important:</strong> This link will expire in 1 hour for security reasons.</p>
                    </div>
                    <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
                </div>
                <div class="footer">
                    <p>This email was sent by {{ app_name }}. If you have any questions, please contact our support team.</p>
                </div>
            </div>
        </body>
        </html>
        """

        template_obj = self.jinja_env.from_string(template)
        return template_obj.render(
            app_name=settings.APP_NAME,
            user_name=user.first_name or user.username,
            reset_url=f"http://localhost:3000/reset-password?token={reset_token}"  # TODO: Make this configurable
        )

    def _create_password_reset_email_text(self, user: User, reset_token: str) -> str:
        """Create text content for password reset email."""
        return f"""
{settings.APP_NAME} - Password Reset Request

Hello {user.first_name or user.username},

We received a request to reset your password for your {settings.APP_NAME} account.

To reset your password, click the following link:
http://localhost:3000/reset-password?token={reset_token}

IMPORTANT: This link will expire in 1 hour for security reasons.

If you didn't request this password reset, please ignore this email. Your password will remain unchanged.

---
This email was sent by {settings.APP_NAME}. If you have any questions, please contact our support team.
        """.strip()

    async def _log_notification(
        self,
        alert_id: int,
        recipient: str,
        notification_type: str,
        success: bool,
        error_message: Optional[str] = None
    ):
        """Log notification attempt to database."""
        try:
            db = SessionLocal()
            try:
                notification = AlertNotification(
                    alert_id=alert_id,
                    notification_type=notification_type,
                    recipient=recipient,
                    status="sent" if success else "failed",
                    error_message=error_message,
                    sent_at=datetime.utcnow() if success else None
                )

                db.add(notification)
                db.commit()

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error logging notification: {e}")


# Global email service instance
email_service = EmailService()
