"""
Enhanced caching service for market data, stock information, and frequently accessed data.
"""
import json
import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
import hashlib
import logging

from ..database import get_redis, CacheManager
from ..config import settings
from ..utils.logger import get_logger

logger = get_logger(__name__)


class CacheService:
    """Enhanced caching service with specialized methods for different data types."""
    
    def __init__(self):
        self.redis = get_redis()
        self.cache_manager = CacheManager()
        
        # Cache TTL settings (in seconds)
        self.ttl_settings = {
            'stock_data': 300,      # 5 minutes
            'market_data': 60,      # 1 minute
            'historical_data': 3600, # 1 hour
            'user_data': 1800,      # 30 minutes
            'watchlist': 600,       # 10 minutes
            'alerts': 300,          # 5 minutes
            'indicators': 900,      # 15 minutes
            'screening': 1800,      # 30 minutes
            'search': 300,          # 5 minutes
        }
    
    # Stock Data Caching
    async def get_stock_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached stock data."""
        key = f"stock:{symbol}"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached stock data for {symbol}: {e}")
        return None
    
    async def set_stock_data(self, symbol: str, data: Dict[str, Any]) -> bool:
        """Cache stock data."""
        key = f"stock:{symbol}"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['stock_data'])
        except Exception as e:
            logger.error(f"Error caching stock data for {symbol}: {e}")
            return False
    
    async def get_multiple_stocks(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get multiple stocks data from cache efficiently."""
        keys = [f"stock:{symbol}" for symbol in symbols]
        try:
            cached_data = await self.cache_manager.get_multiple(keys)
            result = {}
            
            for key, data in cached_data.items():
                symbol = key.split(':')[1]
                try:
                    result[symbol] = json.loads(data)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in cache for {symbol}")
                    
            return result
        except Exception as e:
            logger.error(f"Error getting multiple stocks from cache: {e}")
            return {}
    
    # Market Data Caching
    async def get_market_data(self, data_type: str = "general") -> Optional[Dict[str, Any]]:
        """Get cached market data."""
        key = f"market:{data_type}"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached market data: {e}")
        return None
    
    async def set_market_data(self, data: Dict[str, Any], data_type: str = "general") -> bool:
        """Cache market data."""
        key = f"market:{data_type}"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['market_data'])
        except Exception as e:
            logger.error(f"Error caching market data: {e}")
            return False
    
    # Historical Data Caching
    async def get_historical_data(self, symbol: str, timeframe: str = "1d", days: int = 30) -> Optional[List[Dict[str, Any]]]:
        """Get cached historical data."""
        key = f"historical:{symbol}:{timeframe}:{days}"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached historical data for {symbol}: {e}")
        return None
    
    async def set_historical_data(self, symbol: str, data: List[Dict[str, Any]], timeframe: str = "1d", days: int = 30) -> bool:
        """Cache historical data."""
        key = f"historical:{symbol}:{timeframe}:{days}"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['historical_data'])
        except Exception as e:
            logger.error(f"Error caching historical data for {symbol}: {e}")
            return False
    
    # Technical Indicators Caching
    async def get_indicators(self, symbol: str, indicator_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached technical indicators."""
        key = f"indicators:{symbol}:{indicator_hash}"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached indicators for {symbol}: {e}")
        return None
    
    async def set_indicators(self, symbol: str, data: Dict[str, Any], indicator_hash: str) -> bool:
        """Cache technical indicators."""
        key = f"indicators:{symbol}:{indicator_hash}"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['indicators'])
        except Exception as e:
            logger.error(f"Error caching indicators for {symbol}: {e}")
            return False
    
    # User Data Caching
    async def get_user_watchlists(self, user_id: int) -> Optional[List[Dict[str, Any]]]:
        """Get cached user watchlists."""
        key = f"user:{user_id}:watchlists"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached watchlists for user {user_id}: {e}")
        return None
    
    async def set_user_watchlists(self, user_id: int, data: List[Dict[str, Any]]) -> bool:
        """Cache user watchlists."""
        key = f"user:{user_id}:watchlists"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['watchlist'])
        except Exception as e:
            logger.error(f"Error caching watchlists for user {user_id}: {e}")
            return False
    
    async def get_user_alerts(self, user_id: int) -> Optional[List[Dict[str, Any]]]:
        """Get cached user alerts."""
        key = f"user:{user_id}:alerts"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached alerts for user {user_id}: {e}")
        return None
    
    async def set_user_alerts(self, user_id: int, data: List[Dict[str, Any]]) -> bool:
        """Cache user alerts."""
        key = f"user:{user_id}:alerts"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['alerts'])
        except Exception as e:
            logger.error(f"Error caching alerts for user {user_id}: {e}")
            return False
    
    # Search Results Caching
    async def get_search_results(self, query: str, search_type: str = "stocks") -> Optional[List[Dict[str, Any]]]:
        """Get cached search results."""
        query_hash = hashlib.md5(query.lower().encode()).hexdigest()
        key = f"search:{search_type}:{query_hash}"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached search results: {e}")
        return None
    
    async def set_search_results(self, query: str, data: List[Dict[str, Any]], search_type: str = "stocks") -> bool:
        """Cache search results."""
        query_hash = hashlib.md5(query.lower().encode()).hexdigest()
        key = f"search:{search_type}:{query_hash}"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['search'])
        except Exception as e:
            logger.error(f"Error caching search results: {e}")
            return False
    
    # Screening Results Caching
    async def get_screening_results(self, criteria_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached screening results."""
        key = f"screening:{criteria_hash}"
        try:
            cached_data = await self.cache_manager.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Error getting cached screening results: {e}")
        return None
    
    async def set_screening_results(self, criteria_hash: str, data: Dict[str, Any]) -> bool:
        """Cache screening results."""
        key = f"screening:{criteria_hash}"
        try:
            json_data = json.dumps(data, default=str)
            return await self.cache_manager.set(key, json_data, self.ttl_settings['screening'])
        except Exception as e:
            logger.error(f"Error caching screening results: {e}")
            return False
    
    # Utility Methods
    def create_criteria_hash(self, criteria: Dict[str, Any]) -> str:
        """Create a hash for screening criteria."""
        criteria_str = json.dumps(criteria, sort_keys=True)
        return hashlib.md5(criteria_str.encode()).hexdigest()
    
    def create_indicator_hash(self, parameters: Dict[str, Any]) -> str:
        """Create a hash for indicator parameters."""
        params_str = json.dumps(parameters, sort_keys=True)
        return hashlib.md5(params_str.encode()).hexdigest()
    
    # Cache Management
    async def invalidate_stock_cache(self, symbol: str) -> bool:
        """Invalidate all cache entries for a stock."""
        try:
            patterns = [
                f"stock:{symbol}",
                f"historical:{symbol}:*",
                f"indicators:{symbol}:*"
            ]
            
            for pattern in patterns:
                await self.cache_manager.delete_pattern(pattern)
            
            return True
        except Exception as e:
            logger.error(f"Error invalidating stock cache for {symbol}: {e}")
            return False
    
    async def invalidate_user_cache(self, user_id: int) -> bool:
        """Invalidate all cache entries for a user."""
        try:
            patterns = [
                f"user:{user_id}:*"
            ]
            
            for pattern in patterns:
                await self.cache_manager.delete_pattern(pattern)
            
            return True
        except Exception as e:
            logger.error(f"Error invalidating user cache for {user_id}: {e}")
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return await self.cache_manager.get_cache_stats()
    
    async def warm_up_cache(self, symbols: List[str]) -> Dict[str, bool]:
        """Warm up cache for frequently accessed stocks."""
        results = {}
        
        # This would typically fetch fresh data and cache it
        # For now, just return the current cache status
        for symbol in symbols:
            cached_data = await self.get_stock_data(symbol)
            results[symbol] = cached_data is not None
        
        return results


# Global cache service instance
cache_service = CacheService()
