"""
Database configuration and session management.
"""
from typing import List, Dict, Any
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import StaticPool
import redis
from .config import settings
from .utils.logger import get_logger

logger = get_logger(__name__)

# SQLAlchemy setup
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=StaticPool if "sqlite" in settings.DATABASE_URL else None,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {},
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for SQLAlchemy models
Base = declarative_base()

# Metadata for Alembic migrations
metadata = MetaData()

# Redis connection
redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)


def get_db():
    """
    Dependency to get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """
    Dependency to get Redis client.
    """
    return redis_client


async def init_db():
    """
    Initialize database tables.
    """
    # Import all models to ensure they are registered with SQLAlchemy
    from .models import user, stock, watchlist, alert
    
    # Create all tables
    Base.metadata.create_all(bind=engine)


async def close_db():
    """
    Close database connections.
    """
    engine.dispose()
    redis_client.close()


# Database utilities
class DatabaseManager:
    """Database management utilities."""
    
    @staticmethod
    def create_tables():
        """Create all database tables."""
        Base.metadata.create_all(bind=engine)
    
    @staticmethod
    def drop_tables():
        """Drop all database tables."""
        Base.metadata.drop_all(bind=engine)
    
    @staticmethod
    def reset_database():
        """Reset database by dropping and recreating tables."""
        DatabaseManager.drop_tables()
        DatabaseManager.create_tables()


# Cache utilities
class CacheManager:
    """Enhanced Redis cache management utilities with bulk operations support."""

    def __init__(self, redis_client=redis_client):
        self.redis = redis_client

    async def get(self, key: str):
        """Get value from cache."""
        try:
            return self.redis.get(key)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None

    async def set(self, key: str, value: str, ttl: int = settings.CACHE_TTL_SECONDS):
        """Set value in cache with TTL."""
        try:
            return self.redis.setex(key, ttl, value)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False

    async def delete(self, key: str):
        """Delete key from cache."""
        try:
            return self.redis.delete(key)
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False

    async def exists(self, key: str):
        """Check if key exists in cache."""
        try:
            return self.redis.exists(key)
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False

    async def flush_all(self):
        """Flush all cache data."""
        try:
            return self.redis.flushall()
        except Exception as e:
            logger.error(f"Cache flush error: {e}")
            return False

    async def get_multiple(self, keys: List[str]) -> Dict[str, str]:
        """Get multiple values from cache efficiently."""
        try:
            if not keys:
                return {}

            values = self.redis.mget(keys)
            result = {}

            for key, value in zip(keys, values):
                if value is not None:
                    result[key] = value

            return result
        except Exception as e:
            logger.error(f"Cache get_multiple error: {e}")
            return {}

    async def set_multiple(self, key_value_pairs: Dict[str, str], ttl: int = settings.CACHE_TTL_SECONDS):
        """Set multiple values in cache efficiently."""
        try:
            if not key_value_pairs:
                return True

            # Use pipeline for efficiency
            pipe = self.redis.pipeline()

            for key, value in key_value_pairs.items():
                pipe.setex(key, ttl, value)

            pipe.execute()
            return True
        except Exception as e:
            logger.error(f"Cache set_multiple error: {e}")
            return False

    async def delete_pattern(self, pattern: str):
        """Delete all keys matching a pattern."""
        try:
            keys = self.redis.keys(pattern)
            if keys:
                return self.redis.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Cache delete_pattern error for pattern {pattern}: {e}")
            return 0

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            info = self.redis.info()
            return {
                'used_memory': info.get('used_memory_human', 'Unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info.get('keyspace_hits', 0), info.get('keyspace_misses', 0))
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}

    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate."""
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0

    def get_market_data_key(self, symbol: str, timeframe: str = "1d"):
        """Generate cache key for market data."""
        return f"market_data:{symbol}:{timeframe}"

    def get_screening_results_key(self, criteria_hash: str):
        """Generate cache key for screening results."""
        return f"screening_results:{criteria_hash}"

    def get_user_watchlist_key(self, user_id: int):
        """Generate cache key for user watchlist."""
        return f"user_watchlist:{user_id}"

    def get_bulk_quotes_key(self, symbols: List[str]) -> str:
        """Generate cache key for bulk quotes."""
        symbols_hash = hash(tuple(sorted(symbols)))
        return f"bulk_quotes:{symbols_hash}"

    def get_trending_stocks_key(self, limit: int) -> str:
        """Generate cache key for trending stocks."""
        return f"trending_stocks:{limit}"

    def get_market_status_key(self) -> str:
        """Generate cache key for market status."""
        return "market_status"


# Global cache manager instance
cache_manager = CacheManager()
