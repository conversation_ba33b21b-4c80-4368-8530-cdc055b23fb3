"""
Admin API endpoints for system management and monitoring.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..database import get_db
from ..models.user import User
from ..core.background_tasks import task_manager
from ..services.cache_service import cache_service
from ..core.security import get_current_active_user
from ..utils.logger import get_logger
from ..config import settings

logger = get_logger(__name__)

router = APIRouter()


def get_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to ensure user has admin privileges."""
    # For now, check if user is premium (in production, you'd have a proper admin role)
    if not current_user.is_premium:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user


@router.get("/health")
async def system_health(admin_user: User = Depends(get_admin_user)):
    """Get system health status."""
    try:
        # Check background tasks status
        task_status = {
            "is_running": task_manager.is_running,
            "scheduler_running": task_manager.scheduler.running if task_manager.scheduler else False,
            "jobs": []
        }
        
        if task_manager.scheduler:
            for job in task_manager.scheduler.get_jobs():
                task_status["jobs"].append({
                    "id": job.id,
                    "name": job.name,
                    "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger)
                })
        
        # Get cache statistics
        cache_stats = await cache_service.get_cache_stats()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "background_tasks": task_status,
            "cache": cache_stats,
            "environment": settings.ENVIRONMENT,
            "debug_mode": settings.DEBUG
        }
        
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving system health"
        )


@router.get("/background-tasks")
async def get_background_tasks_status(admin_user: User = Depends(get_admin_user)):
    """Get background tasks status and configuration."""
    try:
        if not task_manager.scheduler:
            return {
                "status": "not_initialized",
                "is_running": False,
                "jobs": []
            }
        
        jobs = []
        for job in task_manager.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "func": job.func.__name__ if job.func else None,
                "trigger": str(job.trigger),
                "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                "max_instances": job.max_instances,
                "misfire_grace_time": job.misfire_grace_time
            })
        
        return {
            "status": "running" if task_manager.is_running else "stopped",
            "is_running": task_manager.is_running,
            "scheduler_running": task_manager.scheduler.running,
            "jobs": jobs,
            "configuration": {
                "market_data_interval": settings.MARKET_DATA_UPDATE_INTERVAL_MINUTES,
                "alert_check_interval": settings.ALERT_CHECK_INTERVAL_MINUTES,
                "screening_interval": settings.SCREENING_INTERVAL_MINUTES
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting background tasks status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving background tasks status"
        )


@router.post("/background-tasks/start")
async def start_background_tasks(admin_user: User = Depends(get_admin_user)):
    """Start background tasks."""
    try:
        if task_manager.is_running:
            return {
                "message": "Background tasks are already running",
                "status": "running"
            }
        
        await task_manager.start()
        
        return {
            "message": "Background tasks started successfully",
            "status": "running",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error starting background tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error starting background tasks"
        )


@router.post("/background-tasks/stop")
async def stop_background_tasks(admin_user: User = Depends(get_admin_user)):
    """Stop background tasks."""
    try:
        if not task_manager.is_running:
            return {
                "message": "Background tasks are already stopped",
                "status": "stopped"
            }
        
        await task_manager.stop()
        
        return {
            "message": "Background tasks stopped successfully",
            "status": "stopped",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error stopping background tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error stopping background tasks"
        )


@router.post("/background-tasks/restart")
async def restart_background_tasks(admin_user: User = Depends(get_admin_user)):
    """Restart background tasks."""
    try:
        # Stop if running
        if task_manager.is_running:
            await task_manager.stop()
        
        # Start again
        await task_manager.start()
        
        return {
            "message": "Background tasks restarted successfully",
            "status": "running",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error restarting background tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error restarting background tasks"
        )


@router.post("/background-tasks/run-job/{job_id}")
async def run_job_now(job_id: str, admin_user: User = Depends(get_admin_user)):
    """Run a specific background job immediately."""
    try:
        if not task_manager.scheduler:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Background task scheduler not initialized"
            )
        
        job = task_manager.scheduler.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        # Run the job immediately
        job.modify(next_run_time=datetime.utcnow())
        
        return {
            "message": f"Job {job_id} scheduled to run immediately",
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error running job {job_id}"
        )


@router.get("/cache/stats")
async def get_cache_stats(admin_user: User = Depends(get_admin_user)):
    """Get cache statistics."""
    try:
        stats = await cache_service.get_cache_stats()
        return {
            "cache_stats": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving cache statistics"
        )


@router.post("/cache/clear")
async def clear_cache(
    pattern: Optional[str] = Query(None, description="Pattern to match keys (e.g., 'stock:*')"),
    admin_user: User = Depends(get_admin_user)
):
    """Clear cache entries."""
    try:
        if pattern:
            # Clear specific pattern
            deleted_count = await cache_service.cache_manager.delete_pattern(pattern)
            return {
                "message": f"Cleared {deleted_count} cache entries matching pattern '{pattern}'",
                "pattern": pattern,
                "deleted_count": deleted_count,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            # Clear all cache
            await cache_service.cache_manager.flush_all()
            return {
                "message": "All cache entries cleared",
                "timestamp": datetime.utcnow().isoformat()
            }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error clearing cache"
        )


@router.post("/cache/warm-up")
async def warm_up_cache(
    symbols: List[str] = Query(..., description="Stock symbols to warm up"),
    admin_user: User = Depends(get_admin_user)
):
    """Warm up cache for specific stocks."""
    try:
        results = await cache_service.warm_up_cache(symbols)
        
        return {
            "message": f"Cache warm-up completed for {len(symbols)} symbols",
            "symbols": symbols,
            "results": results,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error warming up cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error warming up cache"
        )
