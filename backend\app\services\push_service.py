"""
Push notification service for sending real-time notifications to web and mobile clients.
"""
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

try:
    from pywebpush import webpush, WebPushException
    WEBPUSH_AVAILABLE = True
except ImportError:
    WEBPUSH_AVAILABLE = False
    webpush = None
    WebPushException = Exception

from ..config import settings
from ..models.alert import Alert, AlertNotification
from ..models.user import User
from ..database import SessionLocal
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PushNotificationService:
    """Service for sending push notifications."""
    
    def __init__(self):
        # VAPID keys for web push notifications
        # In production, these should be generated and stored securely
        self.vapid_private_key = getattr(settings, 'VAPID_PRIVATE_KEY', None)
        self.vapid_public_key = getattr(settings, 'VAPID_PUBLIC_KEY', None)
        self.vapid_claims = {
            "sub": f"mailto:{settings.FROM_EMAIL}"
        }
        
    async def send_alert_notification(self, alert: Alert, user: User) -> bool:
        """Send push notification for alert to user."""
        try:
            if not user.notification_push:
                logger.info(f"Push notifications disabled for user {user.id}")
                return False
                
            if not self._is_configured():
                logger.warning("Push notification service not configured, skipping push notification")
                return False
            
            # Get user's push subscriptions
            subscriptions = await self._get_user_push_subscriptions(user.id)
            if not subscriptions:
                logger.info(f"No push subscriptions found for user {user.id}")
                return False
            
            # Create notification payload
            payload = self._create_alert_notification_payload(alert)
            
            # Send to all user subscriptions
            success_count = 0
            for subscription in subscriptions:
                try:
                    success = await self._send_web_push(subscription, payload)
                    if success:
                        success_count += 1
                    
                    # Log notification attempt
                    await self._log_notification(
                        alert.id, 
                        subscription.get('endpoint', 'unknown'), 
                        "push", 
                        success
                    )
                    
                except Exception as e:
                    logger.error(f"Error sending push to subscription: {e}")
                    await self._log_notification(
                        alert.id, 
                        subscription.get('endpoint', 'unknown'), 
                        "push", 
                        False, 
                        str(e)
                    )
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending push notification to user {user.id}: {e}")
            return False
    
    async def send_welcome_notification(self, user: User) -> bool:
        """Send welcome push notification to new user."""
        try:
            if not self._is_configured():
                logger.warning("Push notification service not configured, skipping welcome notification")
                return False
            
            subscriptions = await self._get_user_push_subscriptions(user.id)
            if not subscriptions:
                return False
            
            payload = {
                "title": f"Welcome to {settings.APP_NAME}!",
                "body": "Your account has been created successfully. Start creating watchlists and alerts!",
                "icon": "/icon-192x192.png",
                "badge": "/badge-72x72.png",
                "data": {
                    "type": "welcome",
                    "url": "/dashboard"
                }
            }
            
            success_count = 0
            for subscription in subscriptions:
                try:
                    success = await self._send_web_push(subscription, payload)
                    if success:
                        success_count += 1
                except Exception as e:
                    logger.error(f"Error sending welcome push: {e}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending welcome push notification: {e}")
            return False
    
    async def send_market_update_notification(self, user: User, market_data: Dict[str, Any]) -> bool:
        """Send market update notification to user."""
        try:
            if not self._is_configured():
                return False
            
            subscriptions = await self._get_user_push_subscriptions(user.id)
            if not subscriptions:
                return False
            
            payload = {
                "title": "Market Update",
                "body": f"Market summary: {market_data.get('summary', 'Check your dashboard for updates')}",
                "icon": "/icon-192x192.png",
                "badge": "/badge-72x72.png",
                "data": {
                    "type": "market_update",
                    "url": "/dashboard",
                    "market_data": market_data
                }
            }
            
            success_count = 0
            for subscription in subscriptions:
                try:
                    success = await self._send_web_push(subscription, payload)
                    if success:
                        success_count += 1
                except Exception as e:
                    logger.error(f"Error sending market update push: {e}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending market update notification: {e}")
            return False
    
    def _is_configured(self) -> bool:
        """Check if push notification service is properly configured."""
        if not WEBPUSH_AVAILABLE:
            logger.warning("pywebpush library not available")
            return False
        
        return all([
            self.vapid_private_key,
            self.vapid_public_key
        ])
    
    async def _send_web_push(self, subscription: Dict[str, Any], payload: Dict[str, Any]) -> bool:
        """Send web push notification."""
        try:
            if not WEBPUSH_AVAILABLE:
                return False
            
            # Convert payload to JSON string
            payload_json = json.dumps(payload)
            
            # Send web push
            webpush(
                subscription_info=subscription,
                data=payload_json,
                vapid_private_key=self.vapid_private_key,
                vapid_claims=self.vapid_claims
            )
            
            logger.info(f"Push notification sent successfully to {subscription.get('endpoint', 'unknown')}")
            return True
            
        except WebPushException as e:
            logger.error(f"WebPush error: {e}")
            return False
        except Exception as e:
            logger.error(f"Error sending web push: {e}")
            return False
    
    def _create_alert_notification_payload(self, alert: Alert) -> Dict[str, Any]:
        """Create notification payload for alert."""
        return {
            "title": alert.title,
            "body": alert.message,
            "icon": "/icon-192x192.png",
            "badge": "/badge-72x72.png",
            "data": {
                "type": "alert",
                "alert_id": alert.id,
                "stock_symbol": alert.stock.symbol if alert.stock else "Unknown",
                "alert_type": alert.alert_type.value,
                "trigger_price": alert.trigger_price,
                "triggered_at": alert.triggered_at.isoformat() if alert.triggered_at else None,
                "url": f"/alerts/{alert.id}"
            },
            "actions": [
                {
                    "action": "view",
                    "title": "View Alert"
                },
                {
                    "action": "dismiss",
                    "title": "Dismiss"
                }
            ],
            "requireInteraction": alert.priority.value == "high",
            "silent": False
        }
    
    async def _get_user_push_subscriptions(self, user_id: int) -> List[Dict[str, Any]]:
        """Get push subscriptions for a user."""
        # TODO: Implement push subscription storage in database
        # For now, return empty list - this would be implemented with a UserPushSubscription model
        try:
            db = SessionLocal()
            try:
                # This would query a UserPushSubscription table
                # subscriptions = db.query(UserPushSubscription).filter(
                #     UserPushSubscription.user_id == user_id,
                #     UserPushSubscription.is_active == True
                # ).all()
                # return [sub.to_dict() for sub in subscriptions]
                
                # For now, return empty list
                return []
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting push subscriptions for user {user_id}: {e}")
            return []
    
    async def _log_notification(
        self,
        alert_id: int,
        recipient: str,
        notification_type: str,
        success: bool,
        error_message: Optional[str] = None
    ):
        """Log notification attempt to database."""
        try:
            db = SessionLocal()
            try:
                notification = AlertNotification(
                    alert_id=alert_id,
                    notification_type=notification_type,
                    recipient=recipient,
                    status="sent" if success else "failed",
                    error_message=error_message,
                    sent_at=datetime.utcnow() if success else None
                )
                
                db.add(notification)
                db.commit()
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error logging push notification: {e}")


# Global push service instance
push_service = PushNotificationService()
