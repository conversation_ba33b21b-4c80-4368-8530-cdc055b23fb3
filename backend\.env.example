# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/entryalert
TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/entryalert_test

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys for Market Data
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key
IEX_CLOUD_API_KEY=your_iex_cloud_api_key

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Push Notification Configuration (VAPID keys for web push)
VAPID_PRIVATE_KEY=your_vapid_private_key
VAPID_PUBLIC_KEY=your_vapid_public_key

# Application Settings
APP_NAME=EntryAlert
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# CORS Settings
ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173,http://localhost:3000,http://127.0.0.1:3000

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Background Tasks
SCREENING_INTERVAL_MINUTES=5
MARKET_DATA_UPDATE_INTERVAL_MINUTES=1
ALERT_CHECK_INTERVAL_MINUTES=1

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# WebSocket Settings
WS_HEARTBEAT_INTERVAL=30

# Cache Settings
CACHE_TTL_SECONDS=300
MARKET_DATA_CACHE_TTL=60

# Pagination
DEFAULT_PAGE_SIZE=50
MAX_PAGE_SIZE=1000

# Stock Screening Settings
MAX_STOCKS_PER_SCREEN=8000
SCREENING_TIMEOUT_SECONDS=30

# Notification Settings
MAX_ALERTS_PER_USER_PER_DAY=100
NOTIFICATION_BATCH_SIZE=50
