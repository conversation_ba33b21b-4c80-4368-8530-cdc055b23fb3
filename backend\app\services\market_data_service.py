"""
Market data service for fetching and processing stock data.
"""
import asyncio
import aiohttp
import yfinance as yf
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd

from ..config import settings, MARKET_DATA_APIS
from ..database import cache_manager
from ..utils.logger import get_logger, log_market_data_update

logger = get_logger(__name__)


class MarketDataService:
    """Service for fetching market data from various sources."""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limits = {
            'alpha_vantage': {'requests': 0, 'reset_time': datetime.now()},
            'yahoo_finance': {'requests': 0, 'reset_time': datetime.now()},
        }
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close_session(self):
        """Close HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    def check_rate_limit(self, provider: str) -> bool:
        """Check if we can make a request to the provider."""
        now = datetime.now()
        rate_info = self.rate_limits.get(provider, {})
        
        # Reset counter if enough time has passed
        if now - rate_info.get('reset_time', now) > timedelta(minutes=1):
            self.rate_limits[provider] = {'requests': 0, 'reset_time': now}
            return True
        
        # Check if we're under the limit
        api_config = MARKET_DATA_APIS.get(provider, {})
        max_requests = api_config.get('rate_limit', 100)
        
        return rate_info.get('requests', 0) < max_requests
    
    def increment_rate_limit(self, provider: str):
        """Increment rate limit counter."""
        if provider in self.rate_limits:
            self.rate_limits[provider]['requests'] += 1
    
    async def get_stock_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current stock quote."""
        cache_key = cache_manager.get_market_data_key(symbol, "quote")
        
        # Try cache first
        cached_data = await cache_manager.get(cache_key)
        if cached_data:
            return eval(cached_data)  # Note: In production, use json.loads
        
        # Try Yahoo Finance first (free and reliable)
        try:
            quote_data = await self._fetch_yahoo_quote(symbol)
            if quote_data:
                await cache_manager.set(cache_key, str(quote_data), ttl=60)  # Cache for 1 minute
                log_market_data_update(symbol, "yahoo_finance", True)
                return quote_data
        except Exception as e:
            logger.error(f"Error fetching Yahoo Finance quote for {symbol}: {e}")
        
        # Fallback to Alpha Vantage
        try:
            quote_data = await self._fetch_alpha_vantage_quote(symbol)
            if quote_data:
                await cache_manager.set(cache_key, str(quote_data), ttl=60)
                log_market_data_update(symbol, "alpha_vantage", True)
                return quote_data
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage quote for {symbol}: {e}")
        
        log_market_data_update(symbol, "all_providers", False)
        return None
    
    async def _fetch_yahoo_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch quote from Yahoo Finance."""
        if not self.check_rate_limit('yahoo_finance'):
            return None
        
        try:
            # Use yfinance library for Yahoo Finance data
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info or 'regularMarketPrice' not in info:
                return None
            
            self.increment_rate_limit('yahoo_finance')
            
            return {
                'symbol': symbol.upper(),
                'current_price': info.get('regularMarketPrice'),
                'previous_close': info.get('previousClose'),
                'open_price': info.get('regularMarketOpen'),
                'day_high': info.get('regularMarketDayHigh'),
                'day_low': info.get('regularMarketDayLow'),
                'volume': info.get('regularMarketVolume'),
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'eps': info.get('trailingEps'),
                'dividend_yield': info.get('dividendYield'),
                'beta': info.get('beta'),
                'sector': info.get('sector'),
                'industry': info.get('industry'),
                'name': info.get('longName', info.get('shortName')),
                'exchange': info.get('exchange'),
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Yahoo Finance API error for {symbol}: {e}")
            return None
    
    async def _fetch_alpha_vantage_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch quote from Alpha Vantage."""
        if not settings.ALPHA_VANTAGE_API_KEY or not self.check_rate_limit('alpha_vantage'):
            return None
        
        session = await self.get_session()
        url = MARKET_DATA_APIS['alpha_vantage']['base_url']
        
        params = {
            'function': 'GLOBAL_QUOTE',
            'symbol': symbol,
            'apikey': settings.ALPHA_VANTAGE_API_KEY
        }
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    quote = data.get('Global Quote', {})
                    
                    if not quote:
                        return None
                    
                    self.increment_rate_limit('alpha_vantage')
                    
                    return {
                        'symbol': symbol.upper(),
                        'current_price': float(quote.get('05. price', 0)),
                        'previous_close': float(quote.get('08. previous close', 0)),
                        'open_price': float(quote.get('02. open', 0)),
                        'day_high': float(quote.get('03. high', 0)),
                        'day_low': float(quote.get('04. low', 0)),
                        'volume': int(quote.get('06. volume', 0)),
                        'price_change': float(quote.get('09. change', 0)),
                        'price_change_percent': quote.get('10. change percent', '0%').replace('%', ''),
                        'timestamp': datetime.utcnow().isoformat()
                    }
        except Exception as e:
            logger.error(f"Alpha Vantage API error for {symbol}: {e}")
        
        return None
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> Optional[List[Dict[str, Any]]]:
        """Get historical price data."""
        cache_key = cache_manager.get_market_data_key(symbol, f"history_{period}")
        
        # Try cache first
        cached_data = await cache_manager.get(cache_key)
        if cached_data:
            return eval(cached_data)
        
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            
            if hist.empty:
                return None
            
            # Convert to list of dictionaries
            historical_data = []
            for date, row in hist.iterrows():
                historical_data.append({
                    'date': date.isoformat(),
                    'open': float(row['Open']),
                    'high': float(row['High']),
                    'low': float(row['Low']),
                    'close': float(row['Close']),
                    'volume': int(row['Volume']),
                    'adjusted_close': float(row['Close'])  # Yahoo Finance adjusts by default
                })
            
            # Cache for 1 hour
            await cache_manager.set(cache_key, str(historical_data), ttl=3600)
            return historical_data
            
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return None
    
    async def get_multiple_quotes(self, symbols: List[str], batch_size: int = 50) -> Dict[str, Dict[str, Any]]:
        """Get quotes for multiple symbols with batching and enhanced error handling."""
        if not symbols:
            return {}

        logger.info(f"Fetching quotes for {len(symbols)} symbols in batches of {batch_size}")

        quotes = {}
        failed_symbols = []

        # Process symbols in batches to avoid overwhelming APIs
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            logger.debug(f"Processing batch {i//batch_size + 1}: {len(batch)} symbols")

            # Check cache first for the entire batch
            cached_quotes = await self._get_cached_quotes(batch)
            quotes.update(cached_quotes)

            # Get symbols that need fresh data
            uncached_symbols = [s for s in batch if s not in cached_quotes]

            if uncached_symbols:
                # Try bulk fetch first (more efficient)
                bulk_quotes = await self._fetch_bulk_quotes(uncached_symbols)
                quotes.update(bulk_quotes)

                # Cache the results
                await self._cache_bulk_quotes(bulk_quotes)

                # Track failed symbols for retry
                failed_batch = [s for s in uncached_symbols if s not in bulk_quotes]
                failed_symbols.extend(failed_batch)

            # Add delay between batches to respect rate limits
            if i + batch_size < len(symbols):
                await asyncio.sleep(0.1)  # 100ms delay between batches

        # Retry failed symbols individually with fallback providers
        if failed_symbols:
            logger.warning(f"Retrying {len(failed_symbols)} failed symbols individually")
            retry_quotes = await self._retry_failed_quotes(failed_symbols)
            quotes.update(retry_quotes)

        logger.info(f"Successfully fetched {len(quotes)}/{len(symbols)} quotes")
        return quotes

    async def _get_cached_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get cached quotes for multiple symbols."""
        cached_quotes = {}

        for symbol in symbols:
            cache_key = cache_manager.get_market_data_key(symbol, "quote")
            cached_data = await cache_manager.get(cache_key)
            if cached_data:
                try:
                    # In production, use json.loads instead of eval
                    quote_data = eval(cached_data)
                    cached_quotes[symbol] = quote_data
                except Exception as e:
                    logger.error(f"Error parsing cached data for {symbol}: {e}")

        if cached_quotes:
            logger.debug(f"Found {len(cached_quotes)} cached quotes")

        return cached_quotes

    async def _fetch_bulk_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Fetch quotes for multiple symbols using bulk API calls."""
        quotes = {}

        # Try Yahoo Finance bulk fetch first
        try:
            yahoo_quotes = await self._fetch_yahoo_bulk_quotes(symbols)
            quotes.update(yahoo_quotes)
        except Exception as e:
            logger.error(f"Error in Yahoo Finance bulk fetch: {e}")

        # For symbols that failed, try individual fetches
        failed_symbols = [s for s in symbols if s not in quotes]
        if failed_symbols:
            individual_tasks = [self._fetch_yahoo_quote(symbol) for symbol in failed_symbols]
            individual_results = await asyncio.gather(*individual_tasks, return_exceptions=True)

            for symbol, result in zip(failed_symbols, individual_results):
                if isinstance(result, dict) and result:
                    quotes[symbol] = result

        return quotes

    async def _fetch_yahoo_bulk_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Fetch multiple quotes from Yahoo Finance efficiently."""
        if not self.check_rate_limit('yahoo_finance'):
            logger.warning("Yahoo Finance rate limit exceeded")
            return {}

        quotes = {}

        try:
            # Use yfinance to fetch multiple tickers at once
            tickers = yf.Tickers(' '.join(symbols))

            for symbol in symbols:
                try:
                    ticker = tickers.tickers[symbol]
                    info = ticker.info

                    if info and 'regularMarketPrice' in info:
                        quotes[symbol] = {
                            'symbol': symbol.upper(),
                            'current_price': info.get('regularMarketPrice'),
                            'previous_close': info.get('previousClose'),
                            'open_price': info.get('regularMarketOpen'),
                            'day_high': info.get('regularMarketDayHigh'),
                            'day_low': info.get('regularMarketDayLow'),
                            'volume': info.get('regularMarketVolume'),
                            'market_cap': info.get('marketCap'),
                            'pe_ratio': info.get('trailingPE'),
                            'eps': info.get('trailingEps'),
                            'dividend_yield': info.get('dividendYield'),
                            'beta': info.get('beta'),
                            'sector': info.get('sector'),
                            'industry': info.get('industry'),
                            'timestamp': datetime.now().isoformat()
                        }
                        self.increment_rate_limit('yahoo_finance')

                except Exception as e:
                    logger.debug(f"Error fetching {symbol} in bulk: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in Yahoo Finance bulk fetch: {e}")

        return quotes

    async def _cache_bulk_quotes(self, quotes: Dict[str, Dict[str, Any]]):
        """Cache multiple quotes efficiently."""
        cache_tasks = []

        for symbol, quote_data in quotes.items():
            cache_key = cache_manager.get_market_data_key(symbol, "quote")
            cache_tasks.append(cache_manager.set(cache_key, str(quote_data), ttl=60))

        if cache_tasks:
            await asyncio.gather(*cache_tasks, return_exceptions=True)
            logger.debug(f"Cached {len(cache_tasks)} quotes")

    async def _retry_failed_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Retry failed symbols with fallback providers."""
        quotes = {}

        for symbol in symbols:
            # Try Alpha Vantage as fallback
            try:
                quote_data = await self._fetch_alpha_vantage_quote(symbol)
                if quote_data:
                    quotes[symbol] = quote_data
                    # Cache the result
                    cache_key = cache_manager.get_market_data_key(symbol, "quote")
                    await cache_manager.set(cache_key, str(quote_data), ttl=60)
                    log_market_data_update(symbol, "alpha_vantage", True)
            except Exception as e:
                logger.error(f"Error fetching {symbol} from Alpha Vantage: {e}")
                log_market_data_update(symbol, "all_providers", False)

        return quotes

    async def get_bulk_historical_data(self, symbols: List[str], period: str = "1mo",
                                     batch_size: int = 20) -> Dict[str, List[Dict[str, Any]]]:
        """Get historical data for multiple symbols efficiently."""
        if not symbols:
            return {}

        logger.info(f"Fetching historical data for {len(symbols)} symbols (period: {period})")

        historical_data = {}

        # Process in smaller batches for historical data (more resource intensive)
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            logger.debug(f"Processing historical data batch {i//batch_size + 1}: {len(batch)} symbols")

            # Check cache first
            for symbol in batch:
                cache_key = cache_manager.get_market_data_key(symbol, f"history_{period}")
                cached_data = await cache_manager.get(cache_key)
                if cached_data:
                    try:
                        historical_data[symbol] = eval(cached_data)
                    except Exception as e:
                        logger.error(f"Error parsing cached historical data for {symbol}: {e}")

            # Get uncached symbols
            uncached_symbols = [s for s in batch if s not in historical_data]

            if uncached_symbols:
                # Fetch historical data for uncached symbols
                batch_tasks = [self.get_historical_data(symbol, period) for symbol in uncached_symbols]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                for symbol, result in zip(uncached_symbols, batch_results):
                    if isinstance(result, list) and result:
                        historical_data[symbol] = result

            # Add delay between batches
            if i + batch_size < len(symbols):
                await asyncio.sleep(0.5)  # 500ms delay for historical data

        logger.info(f"Successfully fetched historical data for {len(historical_data)}/{len(symbols)} symbols")
        return historical_data

    async def refresh_market_data_cache(self, symbols: List[str]) -> Dict[str, bool]:
        """Refresh market data cache for given symbols."""
        logger.info(f"Refreshing market data cache for {len(symbols)} symbols")

        results = {}

        # Clear existing cache entries
        for symbol in symbols:
            quote_key = cache_manager.get_market_data_key(symbol, "quote")
            await cache_manager.delete(quote_key)

        # Fetch fresh data
        fresh_quotes = await self.get_multiple_quotes(symbols)

        for symbol in symbols:
            results[symbol] = symbol in fresh_quotes

        success_count = sum(results.values())
        logger.info(f"Successfully refreshed cache for {success_count}/{len(symbols)} symbols")

        return results

    async def get_market_status(self) -> Dict[str, Any]:
        """Get current market status and trading hours."""
        cache_key = "market_status"
        cached_status = await cache_manager.get(cache_key)

        if cached_status:
            try:
                return eval(cached_status)
            except Exception:
                pass

        # Fetch market status (simplified implementation)
        try:
            # In production, you'd use a proper market status API
            now = datetime.now()
            market_status = {
                'is_open': 9 <= now.hour < 16 and now.weekday() < 5,  # Simplified
                'next_open': None,
                'next_close': None,
                'timezone': 'US/Eastern',
                'last_updated': now.isoformat()
            }

            # Cache for 5 minutes
            await cache_manager.set(cache_key, str(market_status), ttl=300)
            return market_status

        except Exception as e:
            logger.error(f"Error fetching market status: {e}")
            return {
                'is_open': False,
                'error': 'Unable to determine market status',
                'last_updated': datetime.now().isoformat()
            }

    async def get_trending_stocks(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get trending stocks (most active/volatile)."""
        cache_key = f"trending_stocks_{limit}"
        cached_trending = await cache_manager.get(cache_key)

        if cached_trending:
            try:
                return eval(cached_trending)
            except Exception:
                pass

        # In production, you'd use a proper trending stocks API
        # For now, return some popular stocks
        popular_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
            'PYPL', 'ADBE', 'CRM', 'ZOOM', 'SPOT', 'SQ', 'ROKU', 'TWTR',
            'UBER', 'LYFT', 'SNAP', 'PINS'
        ][:limit]

        trending_data = await self.get_multiple_quotes(popular_symbols)
        trending_list = []

        for symbol, data in trending_data.items():
            if data:
                trending_list.append({
                    'symbol': symbol,
                    'name': data.get('name', f'{symbol} Inc.'),
                    'current_price': data.get('current_price'),
                    'change_percent': data.get('change_percent', 0),
                    'volume': data.get('volume')
                })

        # Sort by volume (most active first)
        trending_list.sort(key=lambda x: x.get('volume', 0), reverse=True)

        # Cache for 10 minutes
        await cache_manager.set(cache_key, str(trending_list), ttl=600)

        return trending_list

    async def search_stocks(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for stocks by symbol or name with enhanced functionality."""
        if not query or len(query.strip()) < 1:
            return []

        query = query.strip().upper()
        cache_key = f"stock_search:{query}:{limit}"

        # Check cache first
        cached_results = await cache_manager.get(cache_key)
        if cached_results:
            try:
                return eval(cached_results)
            except Exception:
                pass

        search_results = []

        try:
            # First, try to search in our database of stocks
            search_results.extend(await self._search_database_stocks(query, limit))

            # If we don't have enough results, try external APIs
            if len(search_results) < limit:
                remaining_limit = limit - len(search_results)
                external_results = await self._search_external_stocks(query, remaining_limit)
                search_results.extend(external_results)

            # Remove duplicates and limit results
            seen_symbols = set()
            unique_results = []
            for result in search_results:
                if result['symbol'] not in seen_symbols:
                    seen_symbols.add(result['symbol'])
                    unique_results.append(result)
                    if len(unique_results) >= limit:
                        break

            # Cache results for 1 hour
            await cache_manager.set(cache_key, str(unique_results), ttl=3600)

            logger.debug(f"Found {len(unique_results)} results for query '{query}'")
            return unique_results

        except Exception as e:
            logger.error(f"Error searching stocks for query '{query}': {e}")
            return []

    async def _search_database_stocks(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Search stocks in our database."""
        # This would typically query the database
        # For now, return some common stocks that match the query
        common_stocks = {
            'AAPL': {'name': 'Apple Inc.', 'exchange': 'NASDAQ', 'sector': 'Technology'},
            'MSFT': {'name': 'Microsoft Corporation', 'exchange': 'NASDAQ', 'sector': 'Technology'},
            'GOOGL': {'name': 'Alphabet Inc.', 'exchange': 'NASDAQ', 'sector': 'Technology'},
            'AMZN': {'name': 'Amazon.com Inc.', 'exchange': 'NASDAQ', 'sector': 'Consumer Discretionary'},
            'TSLA': {'name': 'Tesla Inc.', 'exchange': 'NASDAQ', 'sector': 'Consumer Discretionary'},
            'META': {'name': 'Meta Platforms Inc.', 'exchange': 'NASDAQ', 'sector': 'Technology'},
            'NVDA': {'name': 'NVIDIA Corporation', 'exchange': 'NASDAQ', 'sector': 'Technology'},
            'NFLX': {'name': 'Netflix Inc.', 'exchange': 'NASDAQ', 'sector': 'Communication Services'},
            'JPM': {'name': 'JPMorgan Chase & Co.', 'exchange': 'NYSE', 'sector': 'Financial Services'},
            'JNJ': {'name': 'Johnson & Johnson', 'exchange': 'NYSE', 'sector': 'Healthcare'},
        }

        results = []

        # Search by symbol
        if query in common_stocks:
            stock_info = common_stocks[query]
            results.append({
                'symbol': query,
                'name': stock_info['name'],
                'exchange': stock_info['exchange'],
                'sector': stock_info['sector'],
                'type': 'Equity',
                'match_type': 'symbol'
            })

        # Search by name (partial matches)
        for symbol, info in common_stocks.items():
            if query in info['name'].upper() and len(results) < limit:
                if symbol != query:  # Avoid duplicates
                    results.append({
                        'symbol': symbol,
                        'name': info['name'],
                        'exchange': info['exchange'],
                        'sector': info['sector'],
                        'type': 'Equity',
                        'match_type': 'name'
                    })

        return results[:limit]

    async def _search_external_stocks(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Search stocks using external APIs."""
        results = []

        try:
            # Try Alpha Vantage symbol search if available
            if self.check_rate_limit('alpha_vantage'):
                alpha_results = await self._search_alpha_vantage(query, limit)
                results.extend(alpha_results)
        except Exception as e:
            logger.debug(f"Alpha Vantage search failed: {e}")

        # If still no results, create a basic result for the query
        if not results and len(query) <= 5:
            results.append({
                'symbol': query,
                'name': f'{query} Corporation',
                'exchange': 'UNKNOWN',
                'sector': 'Unknown',
                'type': 'Equity',
                'match_type': 'fallback'
            })

        return results[:limit]

    async def _search_alpha_vantage(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Search stocks using Alpha Vantage API."""
        if not MARKET_DATA_APIS.get('alpha_vantage', {}).get('api_key'):
            return []

        try:
            session = await self.get_session()
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'SYMBOL_SEARCH',
                'keywords': query,
                'apikey': MARKET_DATA_APIS['alpha_vantage']['api_key']
            }

            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    matches = data.get('bestMatches', [])

                    results = []
                    for match in matches[:limit]:
                        results.append({
                            'symbol': match.get('1. symbol', ''),
                            'name': match.get('2. name', ''),
                            'exchange': match.get('4. region', ''),
                            'type': match.get('3. type', 'Equity'),
                            'match_type': 'external'
                        })

                    self.increment_rate_limit('alpha_vantage')
                    return results

        except Exception as e:
            logger.error(f"Error in Alpha Vantage search: {e}")

        return []

    async def health_check(self) -> Dict[str, Any]:
        """Check the health of market data services."""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'services': {},
            'overall_status': 'healthy'
        }

        # Test Yahoo Finance
        try:
            test_quote = await self._fetch_yahoo_quote('AAPL')
            health_status['services']['yahoo_finance'] = {
                'status': 'healthy' if test_quote else 'degraded',
                'rate_limit_remaining': max(0, 100 - self.rate_limits.get('yahoo_finance', {}).get('requests', 0))
            }
        except Exception as e:
            health_status['services']['yahoo_finance'] = {
                'status': 'unhealthy',
                'error': str(e)
            }

        # Test Alpha Vantage
        try:
            if MARKET_DATA_APIS.get('alpha_vantage', {}).get('api_key'):
                # Simple test - just check rate limit status
                health_status['services']['alpha_vantage'] = {
                    'status': 'healthy' if self.check_rate_limit('alpha_vantage') else 'rate_limited',
                    'rate_limit_remaining': max(0, 100 - self.rate_limits.get('alpha_vantage', {}).get('requests', 0))
                }
            else:
                health_status['services']['alpha_vantage'] = {
                    'status': 'not_configured',
                    'message': 'API key not configured'
                }
        except Exception as e:
            health_status['services']['alpha_vantage'] = {
                'status': 'unhealthy',
                'error': str(e)
            }

        # Determine overall status
        service_statuses = [service['status'] for service in health_status['services'].values()]
        if any(status == 'unhealthy' for status in service_statuses):
            health_status['overall_status'] = 'unhealthy'
        elif any(status in ['degraded', 'rate_limited'] for status in service_statuses):
            health_status['overall_status'] = 'degraded'

        return health_status


# Global service instance
market_data_service = MarketDataService()
